import { OAuth2Config, OA<PERSON>2Provider } from '@rpa-project/shared';

// Get the backend port from environment
const BACKEND_PORT = process.env.PORT || '3002';

// OAuth2 configuration for different providers
export const oauth2Configs: Record<OAuth2Provider, OAuth2Config | null> = {
  eEkonomi: {
    clientId: process.env.EEKONOMI_CLIENT_ID || '',
    clientSecret: process.env.EEKONOMI_CLIENT_SECRET || '',
    authUrl: 'https://identity.vismaonline.com/connect/authorize',
    tokenUrl: 'https://identity.vismaonline.com/connect/token',
    scopes: ['ea:api', 'offline_access'],
    redirectUri: process.env.EEKONOMI_REDIRECT_URI || `http://localhost:${BACKEND_PORT}/api/oauth2/callback/eEkonomi`
  },
  Fortnox: {
    clientId: process.env.FORTNOX_CLIENT_ID || '',
    clientSecret: process.env.FORTNOX_CLIENT_SECRET || '',
    authUrl: 'https://apps.fortnox.se/oauth-v1/auth',
    tokenUrl: 'https://apps.fortnox.se/oauth-v1/token',
    scopes: ['companyinformation', 'bookkeeping', 'archive', 'connectfile'],
    redirectUri: process.env.FORTNOX_REDIRECT_URI || `http://localhost:${BACKEND_PORT}/api/oauth2/callback/Fortnox`
  },
  manual: null // Manual tokens don't use OAuth2
};

/**
 * Get OAuth2 configuration for a provider
 */
export function getOAuth2Config(provider: OAuth2Provider): OAuth2Config | null {
  return oauth2Configs[provider];
}

/**
 * Validate that OAuth2 configuration is complete for a provider
 */
export function validateOAuth2Config(provider: OAuth2Provider): boolean {
  const config = getOAuth2Config(provider);
  if (!config) return false;

  const isValid = !!(
    config.clientId &&
    config.clientSecret &&
    config.authUrl &&
    config.tokenUrl &&
    config.redirectUri
  );

  // Debug logging
  console.log(`OAuth2 validation for ${provider}:`, {
    clientId: config.clientId ? '***SET***' : 'MISSING',
    clientSecret: config.clientSecret ? '***SET***' : 'MISSING',
    authUrl: config.authUrl || 'MISSING',
    tokenUrl: config.tokenUrl || 'MISSING',
    redirectUri: config.redirectUri || 'MISSING',
    isValid
  });

  return isValid;
}

/**
 * Generate OAuth2 authorization URL
 */
export function generateAuthUrl(provider: OAuth2Provider, state: string): string {
  const config = getOAuth2Config(provider);
  if (!config) {
    throw new Error(`OAuth2 configuration not found for provider: ${provider}`);
  }

  // Build parameters in the exact order that Fortnox expects
  const params = new URLSearchParams();

  if (provider === 'Fortnox') {
    // Fortnox parameter order: client_id, redirect_uri, scope, state, access_type, response_type, account_type
    params.append('client_id', config.clientId);
    params.append('redirect_uri', config.redirectUri);
    params.append('scope', config.scopes.join(' '));
    params.append('state', state);
    params.append('access_type', 'offline'); // Required for refresh tokens
    params.append('response_type', 'code');
    // Optional: params.append('account_type', 'service'); // For service accounts
  } else {
    // Standard OAuth2 order for other providers
    params.append('client_id', config.clientId);
    params.append('redirect_uri', config.redirectUri);
    params.append('scope', config.scopes.join(' '));
    params.append('response_type', 'code');
    params.append('state', state);

    if (provider === 'eEkonomi') {
      // Remove problematic parameters for now
      // params.append('prompt', 'select_account');
      // params.append('acr_values', 'service:44643EB1-3F76-4C1C-A672-402AE8085934');
    }
  }

  const authUrl = `${config.authUrl}?${params.toString()}`;
  console.log(`Generated OAuth2 URL for ${provider}:`, authUrl);
  return authUrl;
}

/**
 * Generate state parameter for OAuth2 flow
 */
export function generateState(customerId: string, tokenName: string): string {
  const data = {
    customerId,
    tokenName,
    timestamp: Date.now()
  };
  return Buffer.from(JSON.stringify(data)).toString('base64url');
}

/**
 * Parse state parameter from OAuth2 callback
 */
export function parseState(state: string): { customerId: string; tokenName: string; timestamp: number } {
  try {
    const data = JSON.parse(Buffer.from(state, 'base64url').toString());
    return data;
  } catch (error) {
    throw new Error('Invalid state parameter');
  }
}
