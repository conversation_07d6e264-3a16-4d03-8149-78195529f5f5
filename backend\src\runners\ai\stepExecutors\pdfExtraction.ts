// PDF extraction using pdfjs-dist
import { RpaStep, ExecutionLog, ExtractPdfValuesStep, getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';

/**
 * PDF extraction step executors for AIRunner
 */

export interface PdfExtractionExecutorContext {
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
}

// Use the correct step type from shared package
type ExtractPdfTextStep = ExtractPdfValuesStep;

/**
 * Execute extractPdfValues step - extracts text from PDF and stores it in the specified variable
 */
export async function executeExtractPdfValues(
  step: ExtractPdfTextStep,
  context: PdfExtractionExecutorContext
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables } = context;

  try {
    // Interpolate base64Input to handle variable references
    const interpolatedBase64Input = interpolateVariables(step.base64Input, variables);

    onLog({
      level: 'info',
      message: 'Starting PDF text extraction...',
      stepId: step.id
    });

    // Extract base64 data (remove data URL prefix if present)
    let base64Data = interpolatedBase64Input;
    if (base64Data.includes(',')) {
      base64Data = base64Data.split(',')[1];
    }

    // Convert base64 to buffer
    const pdfBuffer = Buffer.from(base64Data, 'base64');

    onLog({
      level: 'info',
      message: `PDF buffer created, size: ${pdfBuffer.length} bytes`,
      stepId: step.id
    });

    // Extract text from PDF using pdfjs-dist
    let extractedText: string;
    try {
      // Use dynamic import to properly handle ES module
      const pdfjs = await import('pdfjs-dist/legacy/build/pdf.mjs');

      // Configure for Node.js environment
      const pdfjsLib = pdfjs.default || pdfjs;

      const loadingTask = pdfjsLib.getDocument({
        data: new Uint8Array(pdfBuffer),
        useSystemFonts: true,
        disableFontFace: true
      });
      const pdf = await loadingTask.promise;

      let fullText = '';
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        const pageText = textContent.items
          .filter((item: any) => 'str' in item)
          .map((item: any) => item.str)
          .join(' ');
        fullText += pageText + '\n';
      }

      extractedText = fullText.trim();

      onLog({
        level: 'info',
        message: 'PDF text extracted successfully using pdfjs-dist',
        stepId: step.id
      });
    } catch (pdfjsError) {
      throw new Error(`PDF parsing failed: ${pdfjsError instanceof Error ? pdfjsError.message : 'Unknown error'}`);
    }

    onLog({
      level: 'info',
      message: `Extracted ${extractedText.length} characters from PDF`,
      stepId: step.id
    });

    // Use step.variableName or default name to store the extracted text
    const variableName = step.variableName || getDefaultVariableName('extractPdfValues');
    variables[variableName] = extractedText;

    onLog({
      level: 'info',
      message: `PDF text extraction completed. Text stored in variable: ${variableName}`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        [variableName]: extractedText
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Failed to extract PDF text: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
