/**
 * Step-to-runner mapping according to migration plan
 * This file defines which runner handles which step types
 */

/**
 * Runner types available in the system
 */
export enum RunnerType {
  PLAYWRIGHT = 'playwright',
  AI = 'ai',
  API = 'api',
  DATABASE = 'database',
  FILE_SYSTEM = 'file_system'
}

/**
 * Step-to-runner mapping as defined in migration plan
 */
export const STEP_RUNNER_MAPPING = {
  // Navigation
  navigate: 'playwright',
  goBack: 'playwright',
  goForward: 'playwright',
  reload: 'playwright',
  
  // Interaction
  click: 'playwright',
  fill: 'playwright',
  type: 'playwright',
  selectOption: 'playwright',
  check: 'playwright',
  uncheck: 'playwright',
  
  // Waiting
  waitForSelector: 'playwright',
  waitForTimeout: 'playwright',
  waitForUrl: 'playwright',
  
  // Extraction
  extractText: 'playwright',
  extractAttribute: 'playwright',
  takeScreenshot: 'playwright',
  
  // Conditional
  ifElementExists: 'playwright',
  conditionalClick: 'playwright',
  
  // Credentials
  fillPassword: 'playwright',
  fill2FA: 'playwright',
  
  // Files
  downloadFile: 'playwright',
  
  // AI Processing
  extractPdfValues: 'ai',
  processWithLLM: 'ai',

  // API Integration
  apiCall: 'api',
  apiAuth: 'api',
  fortnoxCreateVoucher: 'api',
  fortnoxUploadFile: 'api',
  fortnoxAttachFileToVoucher: 'api',
  fortnoxUploadAndCreateVoucher: 'api'
} as const;

/**
 * Get runner type for a given step type
 */
export function getRunnerForStep(stepType: string): RunnerType {
  const runnerType = STEP_RUNNER_MAPPING[stepType as keyof typeof STEP_RUNNER_MAPPING];
  return (runnerType as RunnerType) || RunnerType.PLAYWRIGHT;
}

/**
 * Check if a step type is supported
 */
export function isStepTypeSupported(stepType: string): boolean {
  return stepType in STEP_RUNNER_MAPPING;
}

/**
 * Get all step types for a given runner
 */
export function getStepTypesForRunner(runnerType: RunnerType): string[] {
  return Object.entries(STEP_RUNNER_MAPPING)
    .filter(([, runner]) => runner === runnerType)
    .map(([stepType]) => stepType);
}

/**
 * Check if a runner type is currently implemented
 */
export function isRunnerImplemented(runnerType: RunnerType): boolean {
  return runnerType === RunnerType.PLAYWRIGHT || runnerType === RunnerType.AI || runnerType === RunnerType.API;
}

/**
 * Get all implemented runner types
 */
export function getImplementedRunnerTypes(): RunnerType[] {
  return [RunnerType.PLAYWRIGHT, RunnerType.AI, RunnerType.API];
}

/**
 * Get all available runner types (including future ones)
 */
export function getAllRunnerTypes(): RunnerType[] {
  return Object.values(RunnerType);
}
