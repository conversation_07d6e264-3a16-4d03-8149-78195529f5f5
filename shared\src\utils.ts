import { RpaStep, ConditionalClickStep, DownloadFileStep, ExtractPdfValuesStep, FortnoxCreateVoucherStep } from './types/steps';

// Base types for validation and API responses
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Utility functions
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function createEmptyFlow(): any {
  return {
    id: generateId(),
    name: '',
    description: '',
    customerId: '',
    steps: [],
    settings: {
      viewport: { width: 1920, height: 1080 },
      timeout: 30000
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };
}

export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  }

  const seconds = Math.floor(ms / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds}s`;
}

// Step creation utilities
export function createStepFromType(stepType: string): RpaStep {
  const baseStep = {
    id: generateId(),
    name: '',
    description: '',
  };

  switch (stepType) {
    case 'navigate':
      return { ...baseStep, type: 'navigate', name: 'Navigera', url: '' }
    case 'goBack':
      return { ...baseStep, type: 'goBack', name: 'Gå tillbaka' }
    case 'goForward':
      return { ...baseStep, type: 'goForward', name: 'Gå framåt' }
    case 'reload':
      return { ...baseStep, type: 'reload', name: 'Ladda om' }
    case 'click':
      return { ...baseStep, type: 'click', name: 'Klicka', selector: '' }
    case 'fill':
      return { ...baseStep, type: 'fill', name: 'Fyll i', selector: '', value: '' }
    case 'type':
      return { ...baseStep, type: 'type', name: 'Skriv', selector: '', text: '' }
    case 'selectOption':
      return { ...baseStep, type: 'selectOption', name: 'Välj alternativ', selector: '' }
    case 'check':
      return { ...baseStep, type: 'check', name: 'Markera', selector: '' }
    case 'uncheck':
      return { ...baseStep, type: 'uncheck', name: 'Avmarkera', selector: '' }
    case 'waitForSelector':
      return { ...baseStep, type: 'waitForSelector', name: 'Vänta på element', selector: '', timeout: 5000 }
    case 'waitForTimeout':
      return { ...baseStep, type: 'waitForTimeout', name: 'Vänta tid', duration: 1000 }
    case 'waitForUrl':
      return { ...baseStep, type: 'waitForUrl', name: 'Vänta på URL', url: '' }
    case 'extractText':
      return { ...baseStep, type: 'extractText', name: 'Extrahera text', selector: '', variableName: getDefaultVariableName('extractText') }
    case 'extractAttribute':
      return { ...baseStep, type: 'extractAttribute', name: 'Extrahera attribut', selector: '', attribute: 'href', variableName: getDefaultVariableName('extractAttribute') }
    case 'takeScreenshot':
      return { ...baseStep, type: 'takeScreenshot', name: 'Ta skärmbild', path: 'screenshot.png', variableName: getDefaultVariableName('takeScreenshot') }
    case 'fillPassword':
      return { ...baseStep, type: 'fillPassword', name: 'Fyll i lösenord', selector: '', credentialId: '' }
    case 'fill2FA':
      return { ...baseStep, type: 'fill2FA', name: 'Fyll i 2FA', selector: '', credentialId: '' }
    case 'ifElementExists':
      return { ...baseStep, type: 'ifElementExists', name: 'Om element finns', selector: '', thenSteps: [], elseSteps: [] }
    case 'conditionalClick':
      return { ...baseStep, type: 'conditionalClick', name: 'Villkorlig klick', selector: '', condition: 'exists' }
    case 'downloadFile':
      return { ...baseStep, type: 'downloadFile', name: 'Ladda ner fil', triggerSelector: '', filename: '', variableName: getDefaultVariableName('downloadFile'), saveToFile: false }
    case 'extractPdfValues':
      return { ...baseStep, type: 'extractPdfValues', name: 'Extrahera PDF-värden', base64Input: '', prompt: 'Extrahera namn, telefonnummer och email från dokumentet', variableName: 'extractedData' }
    case 'fortnoxCreateVoucher':
      return { ...baseStep, type: 'fortnoxCreateVoucher', name: 'Skapa Fortnox Verifikation', inputVariable: '', aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed', voucherSeries: 'A', variableName: 'var-fortnox-voucher' }
    case 'fortnoxUploadFile':
      return { ...baseStep, type: 'fortnoxUploadFile', name: 'Ladda upp fil till Fortnox', inputVariable: '', filename: '', description: 'Ladda upp fil till Fortnox Arkiv', variableName: 'var-fortnox-file' }
    case 'fortnoxAttachFileToVoucher':
      return { ...baseStep, type: 'fortnoxAttachFileToVoucher', name: 'Koppla fil till verifikation', fileIdVariable: '', voucherNumberVariable: '', voucherSeriesVariable: 'A', variableName: 'var-fortnox-attachment' }
    case 'fortnoxUploadAndCreateVoucher':
      return { ...baseStep, type: 'fortnoxUploadAndCreateVoucher', name: 'Ladda upp fil och skapa verifikation', fileInputVariable: '', filename: '', fileDescription: 'Bifogad fil', voucherInputVariable: '', aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed', voucherDescription: 'Verifikation med bifogad fil', voucherSeries: 'A', variableName: 'var-fortnox-voucher-with-file' }
    default:
      throw new Error(`Unknown step type: ${stepType}`)
  }
}

/**
 * Generate default variable name based on step type
 */
export function getDefaultVariableName(stepType: string, stepIndex?: number): string {
  const suffix = stepIndex !== undefined ? `_${stepIndex + 1}` : '';

  switch (stepType) {
    case 'extractText':
      return `extractedText${suffix}`;
    case 'extractAttribute':
      return `extractedAttribute${suffix}`;
    case 'takeScreenshot':
      return `screenshot${suffix}`;
    case 'downloadFile':
      return `downloadedFile${suffix}`;
    case 'extractPdfValues':
      return `extractedData${suffix}`;
    // Fortnox API steps
    case 'fortnoxCreateVoucher':
      return `fortnoxVoucher${suffix}`;
    case 'fortnoxUploadFile':
      return `fortnoxFile${suffix}`;
    case 'fortnoxAttachFileToVoucher':
      return `fortnoxAttachment${suffix}`;
    case 'fortnoxUploadAndCreateVoucher':
      return `fortnoxVoucherWithFile${suffix}`;
    // AI steps
    case 'processWithLLM':
      return `aiProcessed${suffix}`;
    default:
      return `variable${suffix}`;
  }
}

/**
 * Variable interpolation utilities for RPA flows
 */

/**
 * Interpolates variables in a string using ${variableName} syntax
 * @param text The text containing variable references
 * @param variables The variables object containing values
 * @returns The text with variables replaced by their values
 */
export function interpolateVariables(text: string, variables: Record<string, any>): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Replace ${variableName} with actual values
  return text.replace(/\$\{([^}]+)\}/g, (match, variableName) => {
    const trimmedName = variableName.trim();

    if (trimmedName in variables) {
      const value = variables[trimmedName];
      // Convert to string, handling null/undefined
      return value != null ? String(value) : '';
    }

    // If variable not found, leave the placeholder as is
    return match;
  });
}

/**
 * Checks if a string contains variable references
 * @param text The text to check
 * @returns True if the text contains ${variableName} patterns
 */
export function hasVariableReferences(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }
  return /\$\{[^}]+\}/.test(text);
}

/**
 * Extracts all variable names referenced in a string
 * @param text The text to analyze
 * @returns Array of variable names found in the text
 */
export function extractVariableNames(text: string): string[] {
  if (!text || typeof text !== 'string') {
    return [];
  }

  const matches = text.match(/\$\{([^}]+)\}/g);
  if (!matches) {
    return [];
  }

  return matches.map(match => {
    const variableName = match.slice(2, -1).trim(); // Remove ${ and }
    return variableName;
  });
}

/**
 * Validates that all variable references in a text can be resolved
 * @param text The text containing variable references
 * @param variables The available variables
 * @returns Object with validation result and missing variables
 */
export function validateVariableReferences(
  text: string,
  variables: Record<string, any>
): { valid: boolean; missingVariables: string[] } {
  const referencedVariables = extractVariableNames(text);
  const missingVariables = referencedVariables.filter(varName => !(varName in variables));

  return {
    valid: missingVariables.length === 0,
    missingVariables
  };
}

// Step label utilities
export function getStepLabel(step: RpaStep): string {
  switch (step.type) {
    case 'navigate':
      return `Navigate to: ${step.url}`
    case 'click':
      return `Click: ${step.selector}`
    case 'fill':
      return `Fill: ${step.selector} = "${step.value}"`
    case 'type':
      return `Type: ${step.selector} = "${step.text}"`
    case 'waitForSelector':
      return `Wait for: ${step.selector}`
    case 'waitForTimeout':
      return `Wait: ${step.duration}ms`
    case 'extractText':
      return `Extract text: ${step.selector} → ${step.variableName}`
    case 'extractAttribute':
      return `Extract ${step.attribute}: ${step.selector} → ${step.variableName}`
    case 'fillPassword':
      return `Fill password: ${step.selector}`
    case 'fill2FA':
      return `Fill 2FA: ${step.selector}`
    case 'takeScreenshot':
      return `Take screenshot: ${step.path || 'screenshot.png'}`
    case 'ifElementExists':
      return `If element exists: ${step.selector}`
    case 'conditionalClick':
      const conditionalStep = step as ConditionalClickStep
      return `Conditional click: ${conditionalStep.selector} (if ${conditionalStep.condition})`
    case 'downloadFile':
      const downloadStep = step as DownloadFileStep
      return `Download file: ${downloadStep.triggerSelector || 'trigger'} → ${downloadStep.filename || 'file'}`
    case 'extractPdfValues':
      const extractStep = step as ExtractPdfValuesStep
      return `Extract PDF values: ${extractStep.prompt.substring(0, 50)}...`
    case 'fortnoxCreateVoucher':
      const fortnoxStep = step as FortnoxCreateVoucherStep
      return `Create Fortnox voucher: ${fortnoxStep.inputVariable} → ${fortnoxStep.variableName || 'var-fortnox-voucher'}`
    default:
      return step.type
  }
}
