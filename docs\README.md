# RPA Project Documentation

Välkommen till dokumentationen för RPA Project! Här hittar du all information du behöver för att förstå, använda och utveckla systemet.

## 📚 Dokumentationsöversikt

### 🚀 [Project Documentation](project/)
Grundläggande projektinformation och översikt.

- **[README.md](project/README.md)** - Projektöversikt, arkitektur och snabbstart
- **[CHANGELOG.md](project/CHANGELOG.md)** - Versionshistorik och ändringar

### 🛠️ [Development Documentation](development/)
Teknisk dokumentation för utvecklare.

- **[Architecture](development/architecture.md)** - Systemarkitektur och design
- **[Adding New Steps](development/adding-new-steps.md)** - Guide för att lägga till nya RPA-steg
- **[Adding New Runners](development/adding-new-runners.md)** - Guide för att lägga till nya runners
- **[Troubleshooting](development/troubleshooting.md)** - Felsökning och vanliga problem
- **[Conventions](development/conventions.md)** - Kodkonventioner och best practices
- **[Build System](development/build-system.md)** - Build-process och deployment
- **[Migration Plan](development/MIGRATION_PLAN.md)** - Migreringsplan för systemet
- **[Design System](development/design-system-documentation.md)** - UI/UX design system

### 👥 [User Guide](user-guide/)
Användarguider och setup-instruktioner.

- **[OAuth2 Setup](user-guide/OAuth2_SETUP.md)** - Konfiguration av OAuth2-integration
- **[AI Assistant Guide](user-guide/AI_ASSISTANT_README.md)** - Användning av AI-assistenten
- **[LLM Provider Configuration](user-guide/LLM_PROVIDER_CONFIGURATION.md)** - Konfiguration av LLM-providers
- **[LLM Security Guidelines](user-guide/LLM_SECURITY_GUIDELINES.md)** - **VIKTIGT**: Säkerhetsriktlinjer för LLM-providers
- **[LLM Troubleshooting](user-guide/LLM_TROUBLESHOOTING.md)** - Felsökning av LLM-problem

### 🔌 [API Documentation](api/)
Teknisk API-dokumentation.

- **[AI Assistant API](api/AI_ASSISTANT_API.md)** - Komplett API-referens för AI Assistant

### 📖 [Examples](examples/)
Praktiska exempel och implementationer.

- **Custom Runner Example** - Exempel på hur man skapar en custom runner
- **Custom Step Example** - Exempel på hur man skapar ett custom steg
- **Integration Examples** - Exempel på integrationer med externa tjänster

### 📝 [Templates](templates/)
Mallar för snabb utveckling.

- **New Runner Template** - Mall för nya runners
- **New Step Template** - Mall för nya steg

## 🔍 Snabbnavigering

### För nya utvecklare
1. Börja med [Project README](project/README.md) för översikt
2. Läs [Architecture](development/architecture.md) för att förstå systemet
3. Följ [Adding New Steps](development/adding-new-steps.md) för att lära dig utveckla

### För användare
1. Följ [Project README](project/README.md) för installation
2. Konfigurera [LLM Provider](user-guide/LLM_PROVIDER_CONFIGURATION.md) för AI-funktioner
3. Konfigurera [OAuth2 Setup](user-guide/OAuth2_SETUP.md) för externa integrationer
4. Använd [AI Assistant Guide](user-guide/AI_ASSISTANT_README.md) för AI-funktioner

### För felsökning
1. Kolla [Troubleshooting](development/troubleshooting.md) för vanliga problem
2. Använd [LLM Troubleshooting](user-guide/LLM_TROUBLESHOOTING.md) för AI-problem
3. Se [CHANGELOG](project/CHANGELOG.md) för senaste ändringar
4. Använd [Examples](examples/) för referensimplementationer

## 📋 Senaste uppdateringar

### 🤖 LLM Provider-arkitektur (v1.3.1)
- Flexibel LLM provider-arkitektur med stöd för OpenAI och Azure OpenAI
- Enkelt byte mellan providers och modeller via miljövariabler
- **SÄKERHETSFÖRBÄTTRING**: Ingen automatisk provider-switching - systemet byter ALDRIG provider utan explicit användaråtgärd
- Robust felhantering utan automatiska fallbacks mellan providers
- Komplett dokumentation och säkerhetsriktlinjer

### ✅ Variabelväljare Fix (v1.2.0)
- Fixade problem där default-variabelnamn inte visades i variabelväljaren
- Konsistent beteende mellan frontend och backend
- Dokumenterat i [Troubleshooting](development/troubleshooting.md)

### 📁 Dokumentationsorganisation
- All dokumentation flyttad till `docs/` mappen
- Tydlig struktur med kategorier
- Förbättrad navigation och sökbarhet

## 🤝 Bidra till dokumentationen

Dokumentationen är levande och uppdateras kontinuerligt. För att bidra:

1. **Rapportera fel** - Öppna en issue om du hittar fel eller saknad information
2. **Föreslå förbättringar** - Skicka förslag på förbättringar eller nya sektioner
3. **Uppdatera innehåll** - Håll dokumentationen uppdaterad när du gör ändringar i koden

## 📞 Support

Om du inte hittar svaret i dokumentationen:

1. Kolla [Troubleshooting](development/troubleshooting.md)
2. Sök i [Examples](examples/) för liknande implementationer
3. Kontakta utvecklingsteamet

---

*Dokumentationen uppdaterades senast: 2025-07-14*
