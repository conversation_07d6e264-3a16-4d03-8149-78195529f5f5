{"name": "@rpa-project/backend", "version": "1.0.0", "description": "Backend server for RPA project with Express, Playwright, and BullMQ", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@rpa-project/shared": "file:../shared", "@types/better-sqlite3": "^7.6.13", "@types/pdf-parse": "^1.1.5", "axios": "^1.10.0", "better-sqlite3": "^12.2.0", "bullmq": "^4.15.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "morgan": "^1.10.0", "openai": "^5.8.3", "pdf-parse": "^1.1.1", "playwright": "^1.40.1", "redis": "^4.6.11"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/node": "^20.10.4", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.3"}}