import { StepDefinition, StepCategory, STEP_CATEGORIES } from './categories'

export const apiSteps: StepDefinition[] = [
  {
    type: 'fortnoxCreateVoucher',
    name: 'Skapa Fortnox Verifikation',
    icon: '🧾',
    description: '<PERSON>ka<PERSON> en verifikation i Fortnox med kontomappningar'
  },
  {
    type: 'fortnoxUploadFile',
    name: 'Ladda upp fil till Fortnox',
    icon: '📤',
    description: 'Ladda upp en fil till Fortnox arkiv'
  },
  {
    type: 'fortnoxAttachFileToVoucher',
    name: 'Koppla fil till verifikation',
    icon: '📎',
    description: 'Koppla en uppladdad fil till en befintlig verifikation'
  },
  {
    type: 'fortnoxUploadAndCreateVoucher',
    name: 'Ladda upp fil och skapa verifikation',
    icon: '📋',
    description: 'Ladda upp fil och skapa verifikation med automatisk koppling'
  }
]

export const apiCategory: StepCategory = {
  name: STEP_CATEGORIES.API_INTEGRATION,
  icon: '🌐',
  steps: apiSteps
}
